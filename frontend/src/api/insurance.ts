'use client';

import axios from 'axios';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { DocumentDto } from '@/api/documents';

export type InsuranceType =
  | 'homeInsurance'
  | 'contentsInsurance'
  | 'buildingInsurance'
  | 'liabilityInsurance'
  | 'mortgageProtection'
  | 'lifeInsurance'
  | 'other';

export interface InsuranceInfo {
  id: number;
  type: InsuranceType;
  policyProvider: string;
  policyNumber: string;
  coverageAmount: number | null;
  premium: number | null;
  startDate: string | null;
  endDate: string | null;
  userId: number;
  propertyId: number | null;
  documentId: number | null;
  document: DocumentDto | null;
}

export interface InsuranceCreate {
  type: InsuranceType;
  policyProvider: string;
  policyNumber: string;
  coverageAmount?: number | null;
  premium?: number | null;
  startDate?: string | null;
  endDate?: string | null;
  propertyId?: number | null;
  documentId?: number | null;
}

export interface InsuranceUpdate {
  type?: InsuranceType;
  policyProvider?: string;
  policyNumber?: string;
  coverageAmount?: number | null;
  premium?: number | null;
  startDate?: string | null;
  endDate?: string | null;
  propertyId?: number | null;
  documentId?: number | null;
}

export interface InsuranceResponse {
  items: InsuranceInfo[];
  total: number | null;
  page: number | null;
  size: number | null;
  pages: number | null;
}

export async function fetchInsurance({
  token,
  page = 1,
  size = 50,
}: {
  token?: string | null;
  page?: number;
  size?: number;
}): Promise<InsuranceResponse> {
  const response = await axios.get<InsuranceResponse>(getApiUrl(`${API_ENDPOINTS.INSURANCE}/`), {
    params: { page, size },
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to fetch insurance policies');
  }

  return response.data;
}

export async function createInsurance({
  request,
  token,
}: {
  request: InsuranceCreate;
  token?: string | null;
}): Promise<InsuranceInfo> {
  const response = await axios.post<InsuranceInfo>(
    getApiUrl(`${API_ENDPOINTS.INSURANCE}/`),
    request,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.data) {
    throw new Error('Failed to create insurance policy');
  }

  return response.data;
}

export async function updateInsurance({
  insuranceId,
  request,
  token,
}: {
  insuranceId: number;
  request: InsuranceUpdate;
  token?: string | null;
}): Promise<InsuranceInfo> {
  const response = await axios.patch<InsuranceInfo>(
    getApiUrl(`${API_ENDPOINTS.INSURANCE}/${insuranceId}`),
    request,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.data) {
    throw new Error('Failed to update insurance policy');
  }

  return response.data;
}

export function convertFrontendToBackend(
  frontendData: {
    insuranceType: string;
    policyProvider: string;
    policyNumber: string;
    coverageAmount: string;
    premium: string;
  },
  propertyId?: number
): InsuranceCreate {
  return {
    type: frontendData.insuranceType as InsuranceType,
    policyProvider: frontendData.policyProvider || '',
    policyNumber: frontendData.policyNumber || '',
    coverageAmount: frontendData.coverageAmount ? parseFloat(frontendData.coverageAmount) : null,
    premium: frontendData.premium ? parseFloat(frontendData.premium) : null,
    propertyId: propertyId || null,
  };
}

export function convertBackendToFrontend(backendData: InsuranceInfo) {
  const getTypeDisplayName = (type: InsuranceType): string => {
    const typeMap: Record<InsuranceType, string> = {
      homeInsurance: 'Home Insurance',
      contentsInsurance: 'Contents Insurance',
      buildingInsurance: 'Building Insurance',
      liabilityInsurance: 'Liability Insurance',
      mortgageProtection: 'Mortgage Protection',
      lifeInsurance: 'Life Insurance',
      other: 'Other Insurance',
    };
    return typeMap[type] || type;
  };

  return {
    id: backendData.id.toString(),
    title: `${getTypeDisplayName(backendData.type)} - ${backendData.policyProvider}`,
    fields: [
      {
        id: '1',
        label: 'Type',
        value: getTypeDisplayName(backendData.type),
        editable: false,
        type: 'text' as const,
      },
      {
        id: '2',
        label: 'Policy Provider',
        value: backendData.policyProvider || '',
        editable: true,
        type: 'text' as const,
      },
      {
        id: '3',
        label: 'Policy Number',
        value: backendData.policyNumber || '',
        editable: true,
        type: 'text' as const,
      },
      {
        id: '4',
        label: 'Coverage Amount',
        value: backendData.coverageAmount ? `£${backendData.coverageAmount.toLocaleString()}` : '',
        editable: true,
        type: 'text' as const,
      },
      {
        id: '5',
        label: 'Premium',
        value: backendData.premium ? `£${backendData.premium.toLocaleString()}` : '',
        editable: true,
        type: 'text' as const,
      },
    ],
    files: backendData.document ? [backendData.document] : [],
  };
}

export function getInsuranceTypeOptions() {
  return [
    { label: 'Home Insurance', value: 'homeInsurance' },
    { label: 'Contents Insurance', value: 'contentsInsurance' },
    { label: 'Building Insurance', value: 'buildingInsurance' },
    { label: 'Liability Insurance', value: 'liabilityInsurance' },
    { label: 'Mortgage Protection', value: 'mortgageProtection' },
    { label: 'Life Insurance', value: 'lifeInsurance' },
    { label: 'Other', value: 'other' },
  ];
}
