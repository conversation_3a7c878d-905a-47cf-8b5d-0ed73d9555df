'use client';

import axios from 'axios';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { DocumentDto } from '@/api/documents';

export type LegalType =
  | 'tenancyAgreement'
  | 'leaseholdAgreement'
  | 'sharedOwnershipAgreement'
  | 'planningApplicationsAndPermissions'
  | 'partyWallAgreements'
  | 'wills'
  | 'deeds'
  | 'contracts'
  | 'stampDutyReceipt'
  | 'landTitle'
  | 'other';

export interface LegalInfo {
  id: number;
  type: LegalType;
  aiShortSummary: string;
  aiDetailedSummary: string | null;
  generatedPrompts: string | null;
  userId: number;
  propertyId: number | null;
  documentId: number | null;
  document: DocumentDto | null;
}

export interface LegalCreate {
  type: LegalType;
  aiShortSummary?: string;
  aiDetailedSummary?: string | null;
  generatedPrompts?: string | null;
  propertyId?: number | null;
  documentId?: number | null;
}

export interface LegalUpdate {
  type?: LegalType;
  aiShortSummary?: string;
  aiDetailedSummary?: string | null;
  generatedPrompts?: string | null;
  propertyId?: number | null;
  documentId?: number | null;
}

export interface LegalResponse {
  items: LegalInfo[];
  total: number | null;
  page: number | null;
  size: number | null;
  pages: number | null;
}

export async function fetchLegal({
  token,
  page = 1,
  size = 50,
}: {
  token?: string | null;
  page?: number;
  size?: number;
}): Promise<LegalResponse> {
  const response = await axios.get<LegalResponse>(getApiUrl(`${API_ENDPOINTS.LEGAL}/`), {
    params: { page, size },
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to fetch legal documents');
  }

  return response.data;
}

export async function createLegal({
  request,
  token,
}: {
  request: LegalCreate;
  token?: string | null;
}): Promise<LegalInfo> {
  const response = await axios.post<LegalInfo>(getApiUrl(`${API_ENDPOINTS.LEGAL}/`), request, {
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
  });

  if (!response.data) {
    throw new Error('Failed to create legal document');
  }

  return response.data;
}

export async function updateLegal({
  legalId,
  request,
  token,
}: {
  legalId: number;
  request: LegalUpdate;
  token?: string | null;
}): Promise<LegalInfo> {
  const response = await axios.patch<LegalInfo>(
    getApiUrl(`${API_ENDPOINTS.LEGAL}/${legalId}`),
    request,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.data) {
    throw new Error('Failed to update legal document');
  }

  return response.data;
}

export function convertFrontendToBackend(
  frontendData: {
    legalType: string;
    description: string;
    details: string;
  },
  propertyId?: number
): LegalCreate {
  return {
    type: frontendData.legalType as LegalType,
    aiShortSummary: frontendData.description || '',
    aiDetailedSummary: frontendData.details || null,
    propertyId: propertyId || null,
  };
}

export function convertBackendToFrontend(backendData: LegalInfo) {
  const getTypeDisplayName = (type: LegalType): string => {
    const typeMap: Record<LegalType, string> = {
      tenancyAgreement: 'Tenancy Agreement',
      leaseholdAgreement: 'Leasehold Agreement',
      sharedOwnershipAgreement: 'Shared Ownership Agreement',
      planningApplicationsAndPermissions: 'Planning Applications and Permissions',
      partyWallAgreements: 'Party Wall Agreements',
      wills: 'Wills',
      deeds: 'Deeds',
      contracts: 'Contracts',
      stampDutyReceipt: 'Stamp Duty Receipt',
      landTitle: 'Land Title',
      other: 'Other Legal Document',
    };
    return typeMap[type] || type;
  };

  return {
    id: backendData.id.toString(),
    title: getTypeDisplayName(backendData.type),
    fields: [
      {
        id: '1',
        label: 'Type',
        value: getTypeDisplayName(backendData.type),
        editable: false,
        type: 'text' as const,
      },
      {
        id: '2',
        label: 'Summary',
        value: backendData.aiShortSummary || '',
        editable: true,
        type: 'text' as const,
      },
      {
        id: '3',
        label: 'Details',
        value: backendData.aiDetailedSummary || '',
        editable: true,
        type: 'text' as const,
      },
    ],
    files: backendData.document ? [backendData.document] : [],
  };
}

export function getLegalTypeOptions() {
  return [
    { label: 'Tenancy Agreement', value: 'tenancyAgreement' },
    { label: 'Leasehold Agreement', value: 'leaseholdAgreement' },
    { label: 'Shared Ownership Agreement', value: 'sharedOwnershipAgreement' },
    { label: 'Planning Applications and Permissions', value: 'planningApplicationsAndPermissions' },
    { label: 'Party Wall Agreements', value: 'partyWallAgreements' },
    { label: 'Wills', value: 'wills' },
    { label: 'Deeds', value: 'deeds' },
    { label: 'Contracts', value: 'contracts' },
    { label: 'Stamp Duty Receipt', value: 'stampDutyReceipt' },
    { label: 'Land Title', value: 'landTitle' },
    { label: 'Other', value: 'other' },
  ];
}
