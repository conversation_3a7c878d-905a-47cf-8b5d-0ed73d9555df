.container {
	background: #f8f3e9;
	border-radius: 16px;
	padding: 24px 24px 10px 24px;
	width: 100%;
	height: 100%;
	margin: 0 auto;
	box-sizing: border-box;
	box-shadow: none;
	display: flex;
	flex-direction: column;
}

.inlineContainer {
	background: transparent;
	border-radius: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	margin: 0 auto;
	box-sizing: border-box;
	box-shadow: none;
	display: flex;
	flex-direction: column;
}

.header:not(:empty) {
	margin-bottom: 18px;
}

.title {
	font-size: 18px;
	font-weight: bold;
	color: var(--colors-black);
	margin-bottom: 10px;
}

.sectionTitle {
	font-size: 16px;
	font-weight: bold;
	color: var(--colors-black);
	margin-bottom: 12px;
	margin-top: 24px;

	&:first-child {
		margin-top: 0;
	}
}

.summarySection {
	margin-bottom: 24px;
}

.summaryText {
	font-size: 16px;
	line-height: 1.5;
	color: var(--colors-black);
	margin-bottom: 0;
}

.detailedSection {
	margin-bottom: 24px;
}

.titleInput {
	font-size: 18px;
	font-weight: 700;
	color: var(--colors-black);
	margin-bottom: 4px;
	border: 1px solid var(--colors-gray-200);
	border-radius: 8px;
	padding: 10px 8px;
	background: var(--colors-white);
	font-family: 'Quasimoda', sans-serif;
	width: 100%;
	box-sizing: border-box;
	height: 51px;
	max-width: 458px;

	&::placeholder {
		color: #b3b3b3;
		opacity: 1;
	}

	&:focus {
		outline: none;
		border-color: #007a6e;
	}
}

.titleInputWrapper {
	max-width: 458px;
	margin-bottom: 4px;
}

.fieldsContainer {
	width: 100%;
	margin-bottom: 16px;
	display: grid;
	gap: 8px 0;

	@media (max-width: 768px) {
		gap: 16px 0;
	}
}

.fieldRow {
	display: grid;
	grid-template-columns: 188px 1fr auto;
	align-items: center;
	gap: 16px;

	@media (max-width: 767px) {
		grid-template-columns: 1fr auto;
		grid-template-rows: auto auto;
		grid-template-areas:
			'label edit'
			'value edit';
		gap: 4px;
		align-items: flex-start;
	}
}

.fieldLabel {
	font-size: 16px;
	color: var(--colors-neutral-700);

	@media (max-width: 767px) {
		font-weight: 500;
		margin-bottom: 0;
		grid-area: label;
	}
}

.fieldValue {
	font-size: 16px;
	color: var(--colors-black);

	@media (max-width: 767px) {
		grid-area: value;
	}
}

.fieldEdit {
	display: flex;
	justify-content: flex-end;

	@media (max-width: 767px) {
		grid-area: edit;
		justify-content: flex-start;
	}
}

.editButtonContent {
	display: flex;
	align-items: center;
	gap: 8px;
}

.fieldValueContent {
	font-size: 16px;
	color: var(--colors-black);
	white-space: pre-line;
}

.input {
	width: 270px;
	padding: 10px 8px;
	border: 1px solid var(--colors-gray-200);
	border-radius: 8px;
	font-family: 'Quasimoda', sans-serif;
	font-size: 16px;
	color: #222;
	background: var(--colors-white);
	margin-bottom: 0;
	height: 48px;

	&::placeholder {
		color: #b3b3b3;
		opacity: 1;
	}

	&:focus {
		outline: none;
		border-color: #007a6e;
	}

	@media (max-width: 768px) {
		width: 100%;
	}
}

.inputWrapper {
	width: 270px;
	max-width: 100%;

	@media (max-width: 768px) {
		width: 100%;
	}
}

.dropdown {
	width: 270px;

	@media (max-width: 768px) {
		width: 100%;
	}
}

.radioGroup {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
	width: 270px;

	:global([class*='card']) {
		background: var(--colors-white) !important;
	}

	@media (max-width: 768px) {
		width: 100%;
		flex-wrap: nowrap;
		width: inherit;
	}
}

.filesSection {
	margin-bottom: 16px;
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.content {
	flex: 1;
}

.actions {
	display: flex;
	gap: 22px;
	margin-top: auto;
	padding-bottom: 14px;
	gap: 8px;
}

.divider {
	height: 1px;
	background: var(--colors-neutral-100);
	margin: auto 0 14px 0;
}

@media (max-width: 768px) {
	.container {
		padding: 24px 24px 0 24px;
		border-radius: 12px;
		width: 100%;
	}

	.actions {
		gap: 8px;
		padding-bottom: 16px;
	}
}

// Inline editing styles
.inlineEditContainer {
	display: flex;
	flex-direction: column;
	gap: 8px;
	width: 100%;
}

.inlineDisplayContainer {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12px;
	width: 100%;
}

.inlineActions {
	display: flex;
	gap: 8px;
	align-items: center;
}

.inlineInput {
	font-size: 16px;
	color: var(--colors-black);
	border: 1px solid var(--colors-gray-200);
	border-radius: 8px;
	background: var(--colors-white);

	&:focus {
		outline: none;
		border-color: #007a6e;
	}
}

.fieldError {
	font-size: 14px;
	color: var(--colors-red-500);
	font-weight: 500;
}

@media (max-width: 768px) {
	.inlineDisplayContainer {
		flex-direction: column;
		align-items: flex-start;
		gap: 8px;
	}

	.inlineActions {
		width: 100%;
		justify-content: flex-start;
	}
}

.alignLabelsTop .alignTop {
	align-self: start;
	justify-self: start;
	display: block;
	white-space: pre-line;
}