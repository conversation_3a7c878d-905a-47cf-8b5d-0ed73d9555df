import type { <PERSON>a, StoryObj } from '@storybook/react';
import { PersonalizationCard } from './PersonalizationCard';
import { UploadPersonalizationCard } from './components/UploadPersonalizationCard';
import { PersonalizationCardField, PersonalizationCardFile } from './PersonalizationCard.types';
import React from 'react';

const meta: Meta<typeof PersonalizationCard> = {
  title: 'Components/PersonalizationCard',
  component: PersonalizationCard,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof PersonalizationCard>;

const mockFields: PersonalizationCardField[] = [
  {
    id: '1',
    label: 'Brand',
    value: 'Samsung',
    editable: false,
    type: 'text',
    placeholder: 'e.g. Samsung',
  },
  {
    id: '2',
    label: 'Model',
    value: 'RB38T675DB1',
    editable: false,
    type: 'text',
    placeholder: 'Type your fridge model number',
  },
  {
    id: '3',
    label: 'Serial Number',
    value: 'SN123456789',
    editable: true,
    type: 'text',
    placeholder: 'Type your serial number',
  },
  {
    id: '4',
    label: 'Warranty',
    value: '1 year',
    editable: true,
    type: 'text',
    placeholder: 'e.g. 5 years',
  },
];

const mockFiles: PersonalizationCardFile[] = [
  {
    id: 1,
    fileName: 'Fridge - freezer .PDF',
    browserMimeType: 'application/pdf',
    status: 'processingCompleted',
    sizeInKiloBytes: 100,
    createdAt: '2024',
    uploadContext: 'filesPage',
  },
];

export const Default: Story = {
  render: (args) => {
    const [isEditing, setIsEditing] = React.useState(false);
    const [fields, setFields] = React.useState(args.fields);

    return (
      <PersonalizationCard
        {...args}
        fields={fields}
        isEditing={isEditing}
        onEdit={() => setIsEditing(true)}
        onCancel={() => setIsEditing(false)}
        onSave={(updatedFields) => {
          setFields(updatedFields);
          setIsEditing(false);
        }}
        onFileRemove={args.onFileRemove}
      />
    );
  },
  args: {
    fields: mockFields,
  },
};

export const WithFixedWidth: Story = {
  render: (args) => {
    const [isEditing, setIsEditing] = React.useState(false);
    const [fields, setFields] = React.useState(args.fields);

    return (
      <div style={{ width: '798px' }}>
        <PersonalizationCard
          {...args}
          fields={fields}
          isEditing={isEditing}
          onEdit={() => setIsEditing(true)}
          onCancel={() => setIsEditing(false)}
          onSave={(updatedFields) => {
            setFields(updatedFields);
            setIsEditing(false);
          }}
          onFileRemove={args.onFileRemove}
        />
      </div>
    );
  },
  args: {
    fields: mockFields,
  },
};

export const WithFile: Story = {
  render: (args) => {
    const [isEditing, setIsEditing] = React.useState(false);
    const [fields, setFields] = React.useState(args.fields);
    const [files, setFiles] = React.useState(args.files);

    return (
      <PersonalizationCard
        {...args}
        fields={fields}
        files={files}
        isEditing={isEditing}
        onEdit={() => setIsEditing(true)}
        onCancel={() => setIsEditing(false)}
        onSave={(updatedFields, updatedFiles) => {
          setFields(updatedFields);
          setFiles(updatedFiles);
          setIsEditing(false);
        }}
        onFileRemove={(fileId) => {
          if (files) {
            setFiles(files.filter((file) => file.id !== fileId));
          }
        }}
      />
    );
  },
  args: {
    fields: mockFields,
    files: mockFiles,
  },
};

export const UploadCard: Story = {
  render: () => {
    return <UploadPersonalizationCard file={mockFiles[0]} />;
  },
};

const leaseholdFields: PersonalizationCardField[] = [
  {
    id: '1',
    label: 'Premises',
    value:
      'Flat 4, Aurora Court, 99-101 Star Road, London E14 2ND (second floor, 62 m², balcony + parking bay P4)',
    editable: false,
    type: 'text',
  },
  {
    id: '2',
    label: 'Term',
    value: '125 years — 1 June 2025 to 31 May 2150',
    editable: false,
    type: 'text',
  },
  {
    id: '3',
    label: 'Ground Rent',
    value:
      '£250 per annum, reviewed every 25 years in line with CPI (maximum uplift: 2× previous rate)',
    editable: false,
    type: 'text',
  },
  {
    id: '4',
    label: 'Service Charge',
    value:
      'Payable quarterly, variable (2025/26 estimate £1,850 p.a.) covering insurance, communal and structural maintenance, lift servicing, reserve fund',
    editable: false,
    type: 'text',
  },
  {
    id: '5',
    label: 'Leaseholder Covenants',
    value:
      '• Keep flat in good and tenantable repair and redecorate at least every 7 years\n• No sub-lets under 6 months or short-term/Airbnb lettings\n• No structural alterations or window replacements without written consent\n• Permit one well-behaved domestic pet\n• Avoid nuisance or obstruction of common parts',
    editable: false,
    type: 'text',
  },
  {
    id: '6',
    label: 'Freeholder Duties',
    value:
      '• Insure building for full reinstatement value\n• Maintain roof, foundation, façade, lifts and common areas\n• Consult leaseholders on major works (Landlord & Tenant Act 1985, s. 20)',
    editable: false,
    type: 'text',
  },
  {
    id: '7',
    label: 'Assignment / Mortgage',
    value:
      "Permitted at any time; incoming tenant must sign Deed of Covenant and pay Freeholder's reasonable fees (capped at £350 + VAT)",
    editable: false,
    type: 'text',
  },
  {
    id: '8',
    label: 'Access',
    value: "48 hours' written notice (09:00-18:00) except in emergencies",
    editable: false,
    type: 'text',
  },
  {
    id: '9',
    label: 'Forfeiture',
    value:
      "Possible after 21 days' unpaid ground rent or service charge and court order; statutory relief available on full settlement of arrears and costs",
    editable: false,
    type: 'text',
  },
];

const leaseholdFiles: PersonalizationCardFile[] = [
  {
    id: 1,
    fileName: 'Leasehold agreement...PDF',
    browserMimeType: 'application/pdf',
    status: 'processingCompleted',
    sizeInKiloBytes: 2500,
    createdAt: '2024-01-15',
    uploadContext: 'legal',
  },
];

export const WithSummarySection: Story = {
  args: {
    title: 'Document Overview',
    summary: 'This is a summary section that provides a brief overview of the document content.',
    fields: mockFields,
    showSummarySection: true,
    summaryTitle: 'Summary',
  },
};

export const WithSections: Story = {
  args: {
    title: 'Leasehold agreement overview',
    summary:
      '125-year lease: pay £250 ground rent + quarterly service charges, follow the "no Airbnbs, no structural drama" rules, and BlueSky handles the building, falling behind might result in legal disruption.',
    fields: leaseholdFields,
    files: leaseholdFiles,
    showSummarySection: true,
    showDetailedSection: true,
    summaryTitle: 'Summary',
    detailedTitle: 'Detailed',
    filesTitle: 'Your leasehold document',
    readOnly: true,
    alignLabelsTop: true,
  },
};
