import React, { useCallback, useMemo } from 'react';
import { Button } from '@/components/Button';
import { Input } from '@/components/Input';
import { InputState } from '@/components/Input/Input.types';
import { PersonalizationCardField, PersonalizationCardProps } from './PersonalizationCard.types';
import styles from './PersonalizationCard.module.scss';
import { ButtonState, ButtonType, ButtonSize } from '../Button/Button.types';
import { isEmpty } from '@/utils/formValidationUtils';
import { useEditableFields } from '@/hooks/useEditableFields';
import { DocumentPreview } from '@/components/DocumentPreview';
import { format } from 'date-fns';
import { DocumentDto, getUserFriendlyType, isImage } from '@/api/documents';
import { FormRadioCard } from '@/components/FormRadioCard';
import { FormDropdown } from '@/components/FormDropdown';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { PencilEdit01Icon } from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';

const DEFAULT_TITLE = 'Fridge Freezer';
const DEFAULT_FIELD_TYPE = 'text';

export const PersonalizationCard: React.FC<PersonalizationCardProps> = ({
  fields: initialFields,
  files = [],
  onSave,
  className = '',
  isEditing: isEditingProp,
  onEdit,
  onCancel,
  title = DEFAULT_TITLE,
  summary,
  titleEditable,
  inline = false,
  readOnly = false,
  alignLabelsTop = false,
  showSummarySection = false,
  showDetailedSection = false,
  summaryTitle = 'Summary',
  detailedTitle = 'Detailed',
  filesTitle = 'Your documents',
}) => {
  const {
    fields,
    title: editableTitle,
    isEditing,
    userHasStartedEditing,
    isAllFieldsFilled,
    hasEmptyFields,
    handleEdit,
    handleSave: handleSaveFields,
    handleCancel: handleCancelFields,
    handleFieldChange: baseHandleFieldChange,
    handleTitleChange,
  } = useEditableFields({
    initialFields,
    initialTitle: title,
    isEditingProp,
    onEdit,
    onCancel,
    onSave: (updatedFields, updatedTitle) => {
      onSave?.(updatedFields, files, updatedTitle);
    },
  });

  const handleFieldChange = useCallback(
    (id: string, value: string | number) => {
      baseHandleFieldChange(id, value);
    },
    [baseHandleFieldChange]
  );

  // Always show all fields - no hiding logic
  const visibleFields = useMemo(() => {
    return fields.map((field) => {
      return field;
    });
  }, [fields]);

  const handleSave = useCallback(() => {
    handleSaveFields();
  }, [handleSaveFields]);

  const handleCancel = useCallback(() => {
    handleCancelFields();
  }, [handleCancelFields]);

  const renderInput = useCallback(
    (field: PersonalizationCardField, inputClassName: string, isInputDisabled: boolean) => {
      switch (field.type || DEFAULT_FIELD_TYPE) {
        case 'select':
          const currentOption = field.options?.find(
            (option) => String(option.value) === String(field.value)
          );
          const displayValue = currentOption ? currentOption.label : String(field.value);

          return (
            <FormDropdown
              value={displayValue}
              onChange={(selectedLabel) => {
                const selectedOption = field.options?.find(
                  (option) => option.label === selectedLabel
                );
                if (selectedOption) {
                  handleFieldChange(field.id, selectedOption.value);
                }
              }}
              options={field.options?.map((option) => option.label) || []}
              placeholder={field.placeholder || field.label}
              disabled={isInputDisabled}
              className={styles.dropdown}
            />
          );
        case 'radio':
          return (
            <div className={styles.radioGroup}>
              {field.radioOptions?.map((option) => (
                <FormRadioCard
                  key={option.value}
                  label={option.label}
                  checked={String(field.value) === String(option.value)}
                  onChange={() => handleFieldChange(field.id, option.value)}
                  name={field.id}
                  value={String(option.value)}
                />
              ))}
            </div>
          );
        case 'number':
          return (
            <Input
              value={String(field.value || '')}
              onChange={(value) => handleFieldChange(field.id, value ? Number(value) : '')}
              placeholderText={field.placeholder || field.label}
              showLabel={false}
              showHelperText={false}
              showLeftIcon={false}
              inputClassName={inputClassName}
              className={styles.inputWrapper}
              state={isInputDisabled ? InputState.DISABLED : InputState.NORMAL}
              disableClear={true}
            />
          );
        default:
          return (
            <Input
              value={String(field.value || '')}
              onChange={(value) => handleFieldChange(field.id, value)}
              placeholderText={field.placeholder || field.label}
              showLabel={false}
              showHelperText={false}
              showLeftIcon={false}
              inputClassName={inputClassName}
              className={styles.inputWrapper}
              state={isInputDisabled ? InputState.DISABLED : InputState.NORMAL}
              disableClear={true}
            />
          );
      }
    },
    [handleFieldChange]
  );

  const renderFieldValue = useCallback(
    (field: PersonalizationCardField) => {
      // If field has a component, render it
      if (field.type === 'component' && field.component) {
        return field.component;
      }

      // In readOnly mode, always show field value as text
      if (readOnly) {
        return <span className={styles.fieldValueContent}>{field.value || '—'}</span>;
      }

      // Original logic for non-inline mode
      const fieldIsEmpty = isEmpty(field.value);
      const initialField = initialFields.find((f) => f.id === field.id);
      const wasInitiallyEmpty = isEmpty(initialField?.value);

      const shouldShowInput =
        field.editable &&
        (isEditing || (wasInitiallyEmpty && (hasEmptyFields || userHasStartedEditing)));

      if (shouldShowInput) {
        const inputClassName = `${styles.input} ${fieldIsEmpty ? styles.empty : ''}`;
        const isInputDisabled = false;

        return renderInput(field, inputClassName, isInputDisabled);
      }

      return <span className={styles.fieldValueContent}>{field.value}</span>;
    },
    [isEditing, hasEmptyFields, userHasStartedEditing, renderInput, initialFields, readOnly]
  );

  const renderActionButtons = useCallback(() => {
    // In readOnly mode, don't show action buttons
    if (readOnly) {
      return null;
    }

    const saveButtonProps = {
      type: ButtonType.PRIMARY,
      onClick: handleSave,
      state: !isAllFieldsFilled ? ButtonState.DISABLED : ButtonState.DEFAULT,
      disabled: !isAllFieldsFilled,
    };

    if (isEditing || userHasStartedEditing) {
      return (
        <>
          <Button {...saveButtonProps}>Save</Button>
          <Button type={ButtonType.TERTIARY} onClick={handleCancel}>
            Cancel
          </Button>
        </>
      );
    }

    if (hasEmptyFields) {
      return (
        <>
          <Button {...saveButtonProps} state={ButtonState.DISABLED} disabled>
            Save
          </Button>
          <Button type={ButtonType.TERTIARY} onClick={handleEdit}>
            Edit details
          </Button>
        </>
      );
    }

    return (
      <Button type={ButtonType.TERTIARY} onClick={handleEdit}>
        Edit details
      </Button>
    );
  }, [
    isEditing,
    userHasStartedEditing,
    hasEmptyFields,
    isAllFieldsFilled,
    handleSave,
    handleCancel,
    handleEdit,
    readOnly,
  ]);

  return (
    <div
      className={`${inline ? styles.inlineContainer : styles.container} ${alignLabelsTop ? styles.alignLabelsTop : ''} ${className}`}
    >
      <div className={styles.content}>
        <div className={styles.header}>
          {isEditing && titleEditable !== false ? (
            <Input
              value={editableTitle}
              onChange={(value) => handleTitleChange(value)}
              showLabel={false}
              showHelperText={false}
              showLeftIcon={false}
              inputClassName={styles.titleInput}
              className={styles.titleInputWrapper}
              disableClear={true}
              fullWidth
            />
          ) : title ? (
            <h2 className={styles.title}>{title}</h2>
          ) : null}
        </div>

        {showSummarySection && summary && (
          <div className={styles.summarySection}>
            <h3 className={styles.sectionTitle}>{summaryTitle}</h3>
            <p className={styles.summaryText}>{summary}</p>
          </div>
        )}

        {showDetailedSection && visibleFields.length > 0 && (
          <div className={styles.detailedSection}>
            <h3 className={styles.sectionTitle}>{detailedTitle}</h3>
            <div className={styles.fieldsContainer}>
              {visibleFields.map((field) => (
                <div key={field.id} className={styles.fieldRow}>
                  <div className={`${styles.fieldLabel} ${alignLabelsTop ? styles.alignTop : ''}`}>
                    {field.label}
                  </div>
                  <div className={styles.fieldValue}>{renderFieldValue(field)}</div>
                  {field.showEditButton && !readOnly && (
                    <div className={styles.fieldEdit}>
                      <Button
                        size={ButtonSize.SM}
                        type={ButtonType.TERTIARY}
                        onClick={field.onEditClick}
                      >
                        <div className={styles.editButtonContent}>
                          <HugeiconsIcon
                            icon={PencilEdit01Icon as unknown as IconSvgObject}
                            size={16}
                          />
                          Edit
                        </div>
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {!showDetailedSection && visibleFields.length > 0 && (
          <div className={styles.fieldsContainer}>
            {visibleFields.map((field) => (
              <div key={field.id} className={styles.fieldRow}>
                <div className={`${styles.fieldLabel} ${alignLabelsTop ? styles.alignTop : ''}`}>
                  {field.label}
                </div>
                <div className={styles.fieldValue}>{renderFieldValue(field)}</div>
                {field.showEditButton && !readOnly && (
                  <div className={styles.fieldEdit}>
                    <Button
                      size={ButtonSize.SM}
                      type={ButtonType.TERTIARY}
                      onClick={field.onEditClick}
                    >
                      <div className={styles.editButtonContent}>
                        <HugeiconsIcon
                          icon={PencilEdit01Icon as unknown as IconSvgObject}
                          size={16}
                        />
                        Edit
                      </div>
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {files.length > 0 && (
          <div>
            {(showSummarySection || showDetailedSection) && (
              <h3 className={styles.sectionTitle}>{filesTitle}</h3>
            )}
            <div className={styles.filesSection}>
              {files.map((doc: DocumentDto) => (
                <DocumentPreview
                  key={doc.id}
                  filename={doc.fileName}
                  description={getUserFriendlyType(doc)}
                  uploadedOn={doc.createdAt && format(new Date(doc.createdAt), 'dd/MM/yy')}
                  thumbnailDocumentId={doc.id}
                  isImage={isImage(doc)}
                  dark
                  short
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {!inline && (
        <>
          <div className={styles.divider} />
          <div className={styles.actions}>{renderActionButtons()}</div>
        </>
      )}
    </div>
  );
};
