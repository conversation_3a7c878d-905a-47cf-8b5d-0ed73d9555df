import { EditableField } from '@/hooks/useEditableFields';
import { DocumentDto } from '@/api/documents';
import { ReactNode } from 'react';

export type PersonalizationCardFile = DocumentDto;

export type FieldType = 'text' | 'number' | 'select' | 'radio' | 'component';

export interface SelectOption {
  label: string;
  value: string | number;
}

export interface PersonalizationCardField extends EditableField {
  label: string;
  type: FieldType;
  options?: SelectOption[];
  placeholder?: string;
  radioOptions?: SelectOption[];
  component?: ReactNode;
  showEditButton?: boolean;
  onEditClick?: () => void;
}

export interface PersonalizationCardProps {
  title?: string;
  summary?: string;
  fields: PersonalizationCardField[];
  files?: PersonalizationCardFile[];
  onSave?: (
    updatedFields: PersonalizationCardField[],
    files: PersonalizationCardFile[],
    title?: string
  ) => void;
  onFileRemove?: (fileId: string | number) => void;
  className?: string;
  isEditing?: boolean;
  onEdit?: () => void;
  onCancel?: () => void;
  titleEditable?: boolean;
  context?: 'propertyDetails' | 'appliances' | 'insurance' | 'legal';
  inline?: boolean;
  readOnly?: boolean;
  alignLabelsTop?: boolean;
  showSummarySection?: boolean;
  showDetailedSection?: boolean;
  summaryTitle?: string;
  detailedTitle?: string;
  filesTitle?: string;
}
