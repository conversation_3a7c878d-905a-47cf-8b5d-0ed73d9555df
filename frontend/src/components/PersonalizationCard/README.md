# PersonalizationCard Component

The PersonalizationCard component is a flexible card component that displays structured information with optional editing capabilities. It supports various layouts including summary sections, detailed field sections, and file attachments.

## Features

- **Editable Fields**: Support for text, number, select, and radio field types
- **Summary Section**: Display a summary text with optional title
- **Detailed Section**: Structured field display with labels and values
- **File Attachments**: Display and manage document attachments
- **Responsive Design**: Mobile-friendly layout
- **Section-based Layout**: Organize content into logical sections

## Basic Usage

```tsx
import { PersonalizationCard } from '@/components/PersonalizationCard';

const fields = [
  {
    id: '1',
    label: 'Brand',
    value: 'Samsung',
    editable: true,
    type: 'text',
  },
];

<PersonalizationCard
  title="Appliance Details"
  fields={fields}
  onSave={(updatedFields, files, title) => {
    // Handle save
  }}
/>
```

## Advanced Usage with Sections

For complex documents like legal agreements, you can use the section-based layout:

```tsx
<PersonalizationCard
  title="Leasehold Agreement Overview"
  summary="125-year lease: pay £250 ground rent + quarterly service charges, follow the 'no Airbnbs, no structural drama' rules, and BlueSky handles the building."
  fields={detailedFields}
  files={documents}
  showSummarySection={true}
  showDetailedSection={true}
  summaryTitle="Summary"
  detailedTitle="Detailed"
  filesTitle="Your leasehold document"
  readOnly={true}
  alignLabelsTop={true}
/>
```

## Props

### Core Props
- `title?: string` - Main title of the card
- `summary?: string` - Summary text for the summary section
- `fields: PersonalizationCardField[]` - Array of field objects
- `files?: PersonalizationCardFile[]` - Array of file attachments

### Section Control Props
- `showSummarySection?: boolean` - Show the summary section (default: false)
- `showDetailedSection?: boolean` - Show the detailed section with title (default: false)
- `summaryTitle?: string` - Title for summary section (default: "Summary")
- `detailedTitle?: string` - Title for detailed section (default: "Detailed")
- `filesTitle?: string` - Title for files section (default: "Your documents")

### Behavior Props
- `isEditing?: boolean` - Control editing state
- `readOnly?: boolean` - Disable editing entirely
- `inline?: boolean` - Use inline layout without background
- `alignLabelsTop?: boolean` - Align field labels to top for multi-line values

### Event Handlers
- `onSave?: (fields, files, title) => void` - Called when saving changes
- `onEdit?: () => void` - Called when entering edit mode
- `onCancel?: () => void` - Called when canceling edit
- `onFileRemove?: (fileId) => void` - Called when removing a file

## Field Types

### Text Field
```tsx
{
  id: '1',
  label: 'Description',
  value: 'Multi-line text\nwith line breaks',
  editable: true,
  type: 'text',
  placeholder: 'Enter description...'
}
```

### Select Field
```tsx
{
  id: '2',
  label: 'Category',
  value: 'option1',
  editable: true,
  type: 'select',
  options: [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' }
  ]
}
```

### Radio Field
```tsx
{
  id: '3',
  label: 'Type',
  value: 'type1',
  editable: true,
  type: 'radio',
  radioOptions: [
    { label: 'Type 1', value: 'type1' },
    { label: 'Type 2', value: 'type2' }
  ]
}
```

## Styling

The component uses CSS modules with the following key classes:
- `.container` - Main card container
- `.sectionTitle` - Section titles (Summary, Detailed, etc.)
- `.summaryText` - Summary paragraph text
- `.fieldRow` - Individual field rows
- `.fieldLabel` - Field labels
- `.fieldValue` - Field values

## Examples

See the Storybook stories for complete examples:
- `Default` - Basic usage
- `WithSections` - Advanced usage with summary and detailed sections
- `WithFile` - Usage with file attachments

## Migration from Previous Version

If you're upgrading from a previous version, the component remains backward compatible. The new section features are opt-in via the `showSummarySection` and `showDetailedSection` props.
