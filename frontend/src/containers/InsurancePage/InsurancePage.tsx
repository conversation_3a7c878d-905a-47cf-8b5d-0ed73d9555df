'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useInView } from 'react-intersection-observer';

import { Button } from '@/components/Button';
import { ButtonColor, ButtonType } from '@/components/Button/Button.types';
import { PersonalizationCard } from '@/components/PersonalizationCard';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
} from '@/components/PersonalizationCard/PersonalizationCard.types';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { Breadcrumbs } from '@/components/Breadcrumbs';
import { Spinner } from '@/components/Spinner';

import { useWidgets } from '@/hooks/useWidgets';
import { useInsurance } from '@/hooks/useInsurance';
import { InsurancePageProps } from './InsurancePage.types';
import styles from './InsurancePage.module.scss';
import { useFiles } from '@/containers/FilesPage/useFiles';
import { NotificationAlert } from '@/components/NotificationAlert/NotificationAlert';

export const InsurancePage: React.FC<InsurancePageProps> = () => {
  const { getToken } = useAuth();
  const { properties } = useWidgets();
  const {
    insurance,
    editingInsurance,
    hasMoreItems,
    isLoadingMore,
    fetchInsurance,
    loadMoreInsurance,
    createInsurance,
    updateInsurance,
    setEditingInsurance,
    cancelEditingInsurance,
    removeFileFromInsurance,
    addFileBasedInsurance,
  } = useInsurance();

  const [inlineForm, setInlineForm] = useState({
    insuranceType: '',
    policyProvider: '',
    policyNumber: '',
    coverageAmount: '',
    premium: '',
  });
  const [inlineFormTouched, setInlineFormTouched] = useState(false);
  const insuranceContainerRef = useRef<HTMLDivElement>(null);

  const { ref: sentinelRef, inView } = useInView({
    rootMargin: '100px',
    threshold: 0.1,
  });

  const currentPropertyId = properties.length > 0 ? properties[0].id : null;

  const insuranceItems = [
    'Home insurance',
    'Contents insurance',
    'Building insurance',
    'Liability insurance',
    'Mortgage protection insurance',
    'Life insurance',
    'Travel insurance',
    'Pet insurance',
    'Other insurance policies',
  ];

  const loadInsurance = useCallback(async () => {
    const token = await getToken();
    if (token) {
      await fetchInsurance(token, true);
    }
  }, [fetchInsurance, getToken]);

  const { uploadFiles, uploadingFiles, deleteNotification, notifications } = useFiles({
    getUploadContext: 'insurance',
    setUploadContext: 'insurance',
    onChanged: loadInsurance,
    onFileUploaded: (file) => {
      // Create a temporary PersonalizationCard immediately after file upload
      addFileBasedInsurance(file);
    },
  });

  const firstErrorNotification = notifications.find((n) => n.severity === 'error');
  const firstInfoNotification = notifications.find(
    (n) =>
      n.severity === 'info' &&
      !(n.type === 'information_from_document_saved' && n.category === 'insurance')
  );
  const firstWarningNotification = notifications.find((n) => n.severity === 'warning');

  useEffect(() => {
    void loadInsurance();
  }, [loadInsurance]);

  useEffect(() => {
    if (inView && hasMoreItems && !isLoadingMore && insurance.length > 0) {
      getToken().then((token) => {
        token && loadMoreInsurance(token);
      });
    }
  }, [inView, hasMoreItems, isLoadingMore, loadMoreInsurance, getToken, insurance.length]);

  const handleUploadDocument = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    void uploadFiles(fileArray);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSaveInlineInsurance = async () => {
    if (!inlineForm.insuranceType.trim() || !inlineForm.policyProvider.trim()) {
      setInlineFormTouched(true);
      return;
    }

    if (!currentPropertyId) {
      console.error('Missing property ID');
      return;
    }

    try {
      const token = await getToken();
      if (!token) {
        console.error('Failed to get authentication token');
        return;
      }

      await createInsurance(inlineForm, currentPropertyId, token);
      setInlineForm({
        insuranceType: '',
        policyProvider: '',
        policyNumber: '',
        coverageAmount: '',
        premium: '',
      });
      setInlineFormTouched(false);
    } catch (error) {
      console.error('Failed to save insurance policy:', error);
    }
  };

  const handleEdit = (id: string) => () => {
    setEditingInsurance(id);
  };

  const handleCancel = (id: string) => () => {
    cancelEditingInsurance(id);
  };

  const handleSave =
    (id: string) =>
    async (
      updatedFields: PersonalizationCardField[],
      updatedFiles: PersonalizationCardFile[],
      updatedTitle?: string
    ) => {
      try {
        const token = await getToken();
        if (!token) {
          console.error('Failed to get authentication token');
          return;
        }

        await updateInsurance(id, updatedFields, updatedFiles, updatedTitle, token);
        cancelEditingInsurance(id);
      } catch (error) {
        console.error('Failed to update insurance policy:', error);
      }
    };

  const handleFileRemove = (insuranceId: string, fileId: string | number) => {
    removeFileFromInsurance(insuranceId, String(fileId));
  };

  const fileInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className={styles.container}>
      <Breadcrumbs
        path={[{ value: 'Home Profile', url: '/home-profile' }, { value: 'Insurance' }]}
      />

      <h1 className={styles.heading}>Insurance</h1>

      <div className={styles.content}>
        <h2 className={styles.subheading}>Upload documents, receipts, or photos</h2>
        <p className={styles.paragraph}>Examples include insurance policies and documents for:</p>
        <ul className={styles.list}>
          {insuranceItems.map((item, index) => (
            <li key={index} className={styles.listItem}>
              {item}
            </li>
          ))}
        </ul>

        <div className={styles.buttons}>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            onClick={handleUploadDocument}
          >
            Upload documents
          </Button>
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf,.png,.jpg,.jpeg,.webp,.gif,.heic,.heif"
          multiple
          style={{ display: 'none' }}
        />

        {uploadingFiles.length > 0 && (
          <>
            <h2 className={styles.subheading}>Your documents</h2>
            <div className={styles.uploadingFiles}>
              <FileUploadGrid files={uploadingFiles} showRemoveButton={false} />
            </div>
          </>
        )}

        <div className={styles.alerts}>
          {firstErrorNotification && (
            <NotificationAlert notification={firstErrorNotification} onClose={deleteNotification} />
          )}
          {firstWarningNotification && (
            <NotificationAlert
              notification={firstWarningNotification}
              onClose={deleteNotification}
            />
          )}
          {firstInfoNotification && (
            <NotificationAlert notification={firstInfoNotification} onClose={deleteNotification} />
          )}
        </div>
      </div>

      <div className={styles.content} ref={insuranceContainerRef}>
        {insurance.map((insurancePolicy: any) => (
          <div key={insurancePolicy.id} className={styles.PersonalizationCard}>
            <PersonalizationCard
              title={insurancePolicy.title}
              fields={insurancePolicy.fields}
              files={insurancePolicy.files}
              isEditing={editingInsurance.includes(insurancePolicy.id)}
              onEdit={handleEdit(insurancePolicy.id)}
              onCancel={handleCancel(insurancePolicy.id)}
              onSave={handleSave(insurancePolicy.id)}
              onFileRemove={(fileId) => handleFileRemove(insurancePolicy.id, fileId)}
              context="insurance"
            />
          </div>
        ))}
        {hasMoreItems && insurance.length > 0 && <div ref={sentinelRef} style={{ height: 50 }} />}
      </div>

      {isLoadingMore && (
        <div className={styles.loadingSpinner}>
          <Spinner />
        </div>
      )}
    </div>
  );
};
