'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useInView } from 'react-intersection-observer';

import { Button } from '@/components/Button';
import { ButtonColor, ButtonState, ButtonType } from '@/components/Button/Button.types';
import { PersonalizationCard } from '@/components/PersonalizationCard';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
} from '@/components/PersonalizationCard/PersonalizationCard.types';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { Breadcrumbs } from '@/components/Breadcrumbs';
import { Spinner } from '@/components/Spinner';

import { useWidgets } from '@/hooks/useWidgets';
import { useLegal } from '@/hooks/useLegal';
import { LegalPageProps } from './LegalPage.types';
import styles from './LegalPage.module.scss';
import { useFiles } from '@/containers/FilesPage/useFiles';
import { NotificationAlert } from '@/components/NotificationAlert/NotificationAlert';

export const LegalPage: React.FC<LegalPageProps> = () => {
  const { getToken } = useAuth();
  const { properties } = useWidgets();
  const {
    legal,
    editingLegal,
    hasMoreItems,
    isLoadingMore,
    fetchLegal,
    loadMoreLegal,
    updateLegal,
    setEditingLegal,
    cancelEditingLegal,
    removeFileFromLegal,
  } = useLegal();

  const [inlineForm, setInlineForm] = useState({
    legalType: '',
    description: '',
    details: '',
  });
  const [inlineFormTouched, setInlineFormTouched] = useState(false);
  const legalContainerRef = useRef<HTMLDivElement>(null);

  const { ref: sentinelRef, inView } = useInView({
    rootMargin: '100px',
    threshold: 0.1,
  });

  const currentPropertyId = properties.length > 0 ? properties[0].id : null;

  const legalItems = [
    'Tenancy Agreement',
    'Leasehold Agreement',
    'Shared Ownership Agreement',
    'Planning Applications and Permissions',
    'Party Wall Agreements',
    'Deeds',
    'Contracts',
    'Stamp Duty Receipt',
    'Land Title',
    'Other',
  ];

  const loadLegal = useCallback(async () => {
    const token = await getToken();
    if (token) {
      await fetchLegal(token, true);
    }
  }, [fetchLegal, getToken]);

  const { uploadFiles, uploadingFiles, deleteNotification, notifications } = useFiles({
    getUploadContext: 'legal',
    setUploadContext: 'legal',
    onChanged: loadLegal,
  });

  const firstErrorNotification = notifications.find((n) => n.severity === 'error');
  const firstInfoNotification = notifications.find(
    (n) =>
      n.severity === 'info' &&
      !(n.type === 'information_from_document_saved' && n.category === 'legal')
  );
  const firstWarningNotification = notifications.find((n) => n.severity === 'warning');

  useEffect(() => {
    void loadLegal();
  }, [loadLegal]);

  useEffect(() => {
    if (inView && hasMoreItems && !isLoadingMore && legal.length > 0) {
      getToken().then((token) => {
        token && loadMoreLegal(token);
      });
    }
  }, [inView, hasMoreItems, isLoadingMore, loadMoreLegal, getToken, legal.length]);

  const handleUploadDocument = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    void uploadFiles(fileArray);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSaveInlineLegal = async () => {
    if (!inlineForm.legalType.trim()) {
      setInlineFormTouched(true);
      return;
    }

    if (!currentPropertyId) {
      console.error('Missing property ID');
      return;
    }

    try {
      const token = await getToken();
      if (!token) {
        console.error('Failed to get authentication token');
        return;
      }

      setInlineForm({
        legalType: '',
        description: '',
        details: '',
      });
      setInlineFormTouched(false);
    } catch (error) {
      console.error('Failed to save legal document:', error);
    }
  };

  const handleEdit = (id: string) => () => {
    setEditingLegal(id);
  };

  const handleCancel = (id: string) => () => {
    cancelEditingLegal(id);
  };

  const handleSave =
    (id: string) =>
    async (
      updatedFields: PersonalizationCardField[],
      updatedFiles: PersonalizationCardFile[],
      updatedTitle?: string
    ) => {
      try {
        const token = await getToken();
        if (!token) {
          console.error('Failed to get authentication token');
          return;
        }

        await updateLegal(id, updatedFields, updatedFiles, updatedTitle, token);
        cancelEditingLegal(id);
      } catch (error) {
        console.error('Failed to update legal document:', error);
      }
    };

  const handleFileRemove = (legalId: string, fileId: string | number) => {
    removeFileFromLegal(legalId, String(fileId));
  };

  const fileInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className={styles.container}>
      <Breadcrumbs
        path={[{ value: 'Property Profile', url: '/property-profile' }, { value: 'Legal' }]}
      />

      <h1 className={styles.heading}>Legal</h1>

      <div className={styles.content}>
        <h2 className={styles.subheading}>Upload documents</h2>
        <p className={styles.paragraph}>
          Legal documents contain detailed information that helps Alfie provide personalised
          property management for you. Examples:
        </p>
        <ul className={styles.list}>
          {legalItems.map((item, index) => (
            <li key={index} className={styles.listItem}>
              {item}
            </li>
          ))}
        </ul>
        <p className={styles.paragraph}>
          Upload more documents to add a different legal document or to update details on a document
          listed below.
        </p>
        <div className={styles.buttons}>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            onClick={handleUploadDocument}
          >
            Upload documents
          </Button>
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf,.png,.jpg,.jpeg,.webp,.gif,.heic,.heif"
          multiple
          style={{ display: 'none' }}
        />

        {uploadingFiles.length > 0 && (
          <>
            <h2 className={styles.subheading}>Your documents</h2>
            <div className={styles.uploadingFiles}>
              <FileUploadGrid files={uploadingFiles} showRemoveButton={false} />
            </div>
          </>
        )}

        <div className={styles.alerts}>
          {firstErrorNotification && (
            <NotificationAlert notification={firstErrorNotification} onClose={deleteNotification} />
          )}
          {firstWarningNotification && (
            <NotificationAlert
              notification={firstWarningNotification}
              onClose={deleteNotification}
            />
          )}
          {firstInfoNotification && (
            <NotificationAlert notification={firstInfoNotification} onClose={deleteNotification} />
          )}
        </div>
      </div>

      <div className={styles.content} ref={legalContainerRef}>
        {legal.map((legalDoc: any) => (
          <div key={legalDoc.id} className={styles.PersonalizationCard}>
            <PersonalizationCard
              title={legalDoc.title}
              fields={legalDoc.fields}
              files={legalDoc.files}
              isEditing={editingLegal.includes(legalDoc.id)}
              onEdit={handleEdit(legalDoc.id)}
              onCancel={handleCancel(legalDoc.id)}
              onSave={handleSave(legalDoc.id)}
              onFileRemove={(fileId) => handleFileRemove(legalDoc.id, fileId)}
            />
          </div>
        ))}
        {hasMoreItems && legal.length > 0 && <div ref={sentinelRef} style={{ height: 50 }} />}
      </div>

      {isLoadingMore && (
        <div className={styles.loadingSpinner}>
          <Spinner />
        </div>
      )}
    </div>
  );
};
