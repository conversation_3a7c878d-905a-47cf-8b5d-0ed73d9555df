import { create } from 'zustand';
import {
  InsuranceCreate,
  InsuranceInfo,
  InsuranceResponse,
  InsuranceUpdate,
  convertBackendToFrontend,
  convertFrontendToBackend,
  createInsurance,
  fetchInsurance,
  updateInsurance,
} from '@/api/insurance';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
} from '@/components/PersonalizationCard/PersonalizationCard.types';

const DEFAULT_PAGE_SIZE = 10;

interface InsuranceItem {
  id: string;
  title: string;
  fields: PersonalizationCardField[];
  files: PersonalizationCardFile[];
}

interface InsuranceFormData {
  insuranceType: string;
  policyProvider: string;
  policyNumber: string;
  coverageAmount: string;
  premium: string;
}

function createFallbackInsurance(data: InsuranceFormData): InsuranceItem {
  const insuranceId = `insurance-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  return {
    id: insuranceId,
    title: `${data.insuranceType} - ${data.policyProvider}`,
    fields: [
      {
        id: '1',
        label: 'Type',
        value: data.insuranceType,
        editable: false,
        type: 'text' as const,
      },
      {
        id: '2',
        label: 'Policy Provider',
        value: data.policyProvider,
        editable: true,
        type: 'text' as const,
      },
      {
        id: '3',
        label: 'Policy Number',
        value: data.policyNumber,
        editable: true,
        type: 'text' as const,
      },
      {
        id: '4',
        label: 'Coverage Amount',
        value: data.coverageAmount,
        editable: true,
        type: 'text' as const,
      },
      {
        id: '5',
        label: 'Premium',
        value: data.premium,
        editable: true,
        type: 'text' as const,
      },
    ],
    files: [],
  };
}

function mapFieldsToUpdateRequest(
  fields: PersonalizationCardField[],
  title?: string
): InsuranceUpdate {
  const typeField = fields.find((f) => f.label === 'Type');
  const providerField = fields.find((f) => f.label === 'Policy Provider');
  const numberField = fields.find((f) => f.label === 'Policy Number');
  const coverageField = fields.find((f) => f.label === 'Coverage Amount');
  const premiumField = fields.find((f) => f.label === 'Premium');

  return {
    type: typeField?.value as any,
    policyProvider: providerField?.value ? String(providerField.value) : '',
    policyNumber: numberField?.value ? String(numberField.value) : '',
    coverageAmount: coverageField?.value
      ? parseFloat(String(coverageField.value).replace(/[£,]/g, ''))
      : null,
    premium: premiumField?.value
      ? parseFloat(String(premiumField.value).replace(/[£,]/g, ''))
      : null,
  };
}

interface InsuranceState {
  insurance: InsuranceItem[];
  editingInsurance: string[];

  currentPage: number;
  totalPages: number | null;
  hasMoreItems: boolean;
  isLoadingMore: boolean;

  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isFetching: boolean;

  fetchInsurance: (token: string, reset?: boolean) => Promise<void>;
  loadMoreInsurance: (token: string) => Promise<void>;
  createInsurance: (data: InsuranceFormData, propertyId?: number, token?: string) => Promise<void>;
  updateInsurance: (
    id: string,
    updatedFields: PersonalizationCardField[],
    updatedFiles: PersonalizationCardFile[],
    updatedTitle: string | undefined,
    token: string
  ) => Promise<void>;

  setEditingInsurance: (id: string) => void;
  cancelEditingInsurance: (id: string) => void;
  removeFileFromInsurance: (insuranceId: string, fileId: string) => void;
  addFileBasedInsurance: (file: PersonalizationCardFile) => void;

  reset: () => void;
}

export const useInsurance = create<InsuranceState>((set, get) => ({
  insurance: [],
  editingInsurance: [],

  currentPage: 1,
  totalPages: null,
  hasMoreItems: true,
  isLoadingMore: false,

  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isFetching: false,

  fetchInsurance: async (token: string, reset?: boolean) => {
    set({ isFetching: true });
    try {
      if (reset) {
        set({ insurance: [], currentPage: 1 });
      }
      const response: InsuranceResponse = await fetchInsurance({
        token,
        page: reset ? 1 : get().currentPage,
        size: DEFAULT_PAGE_SIZE,
      });
      const convertedInsurance = response.items.map(convertBackendToFrontend);
      set((state) => ({
        insurance: reset ? convertedInsurance : [...state.insurance, ...convertedInsurance],
        totalPages: response.pages,
        hasMoreItems:
          response.page !== null && response.pages !== null && response.page < response.pages,
      }));
    } catch (error) {
      console.error('Failed to fetch insurance policies:', error);
    } finally {
      set({ isFetching: false });
    }
  },

  loadMoreInsurance: async (token: string) => {
    set({ isLoadingMore: true });
    try {
      const currentPage = get().currentPage;
      const response: InsuranceResponse = await fetchInsurance({
        token,
        page: currentPage + 1,
        size: DEFAULT_PAGE_SIZE,
      });
      const convertedInsurance = response.items.map(convertBackendToFrontend);
      set((state) => ({
        insurance: [...state.insurance, ...convertedInsurance],
        totalPages: response.pages,
        hasMoreItems:
          response.page !== null && response.pages !== null && response.page < response.pages,
        currentPage: currentPage + 1,
      }));
    } catch (error) {
      console.error('Failed to load more insurance policies:', error);
    } finally {
      set({ isLoadingMore: false });
    }
  },

  createInsurance: async (data: InsuranceFormData, propertyId?: number, token?: string) => {
    set({ isCreating: true });
    try {
      if (!token) {
        throw new Error('Authentication token is required');
      }

      const backendRequest: InsuranceCreate = convertFrontendToBackend(data, propertyId);
      const createdInsurance: InsuranceInfo = await createInsurance({
        request: backendRequest,
        token,
      });

      const convertedInsurance = convertBackendToFrontend(createdInsurance);
      set((state) => ({
        insurance: [...state.insurance, convertedInsurance],
      }));
    } catch (error) {
      console.error('Failed to create insurance policy:', error);
      const fallbackInsurance = createFallbackInsurance(data);

      set((state) => ({
        insurance: [...state.insurance, fallbackInsurance],
      }));
    } finally {
      set({ isCreating: false });
    }
  },

  updateInsurance: async (
    id: string,
    updatedFields: PersonalizationCardField[],
    updatedFiles: PersonalizationCardFile[],
    updatedTitle: string | undefined,
    token: string
  ) => {
    set({ isUpdating: true });
    try {
      const insuranceId = parseInt(id);
      if (isNaN(insuranceId)) {
        throw new Error('Invalid insurance policy ID');
      }

      const updateRequest: InsuranceUpdate = mapFieldsToUpdateRequest(updatedFields, updatedTitle);

      const updatedInsurance: InsuranceInfo = await updateInsurance({
        insuranceId,
        request: updateRequest,
        token,
      });

      const convertedInsurance = convertBackendToFrontend(updatedInsurance);

      set((state) => ({
        insurance: state.insurance.map((insurancePolicy) =>
          insurancePolicy.id === id ? convertedInsurance : insurancePolicy
        ),
      }));
    } catch (error) {
      console.error('Failed to update insurance policy:', error);
      set((state) => ({
        insurance: state.insurance.map((insurancePolicy) =>
          insurancePolicy.id === id
            ? {
                ...insurancePolicy,
                title: updatedTitle || insurancePolicy.title,
                fields: updatedFields,
                files: updatedFiles,
              }
            : insurancePolicy
        ),
      }));
    } finally {
      set({ isUpdating: false });
    }
  },

  setEditingInsurance: (id: string) => {
    set((state) => ({
      editingInsurance: [...state.editingInsurance, id],
    }));
  },

  cancelEditingInsurance: (id: string) => {
    set((state) => ({
      editingInsurance: state.editingInsurance.filter((insuranceId) => insuranceId !== id),
    }));
  },

  removeFileFromInsurance: (insuranceId: string, fileId: string) => {
    set((state) => ({
      insurance: state.insurance.map((insurancePolicy) =>
        insurancePolicy.id === insuranceId
          ? {
              ...insurancePolicy,
              files: insurancePolicy.files.filter((file) => file.id.toString() !== fileId),
            }
          : insurancePolicy
      ),
    }));
  },

  addFileBasedInsurance: (file: PersonalizationCardFile) => {
    const insuranceId = `insurance-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const newInsurance: InsuranceItem = {
      id: insuranceId,
      title: file.fileName.replace(/\.[^/.]+$/, ''),
      fields: [
        {
          id: '1',
          label: 'Type',
          value: '',
          editable: true,
          placeholder: 'e.g. Home Insurance',
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Policy Provider',
          value: '',
          editable: true,
          placeholder: 'Insurance company name',
          type: 'text' as const,
        },
        {
          id: '3',
          label: 'Policy Number',
          value: '',
          editable: true,
          placeholder: 'Policy number',
          type: 'text' as const,
        },
        {
          id: '4',
          label: 'Coverage Amount',
          value: '',
          editable: true,
          placeholder: 'e.g. £500,000',
          type: 'text' as const,
        },
        {
          id: '5',
          label: 'Premium',
          value: '',
          editable: true,
          placeholder: 'e.g. £1,200',
          type: 'text' as const,
        },
      ],
      files: [file],
    };

    set((state) => ({
      insurance: [...state.insurance, newInsurance],
      editingInsurance: [...state.editingInsurance, insuranceId],
    }));
  },

  reset: () => {
    set({
      insurance: [],
      editingInsurance: [],
      currentPage: 1,
      totalPages: null,
      hasMoreItems: true,
      isLoadingMore: false,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isFetching: false,
    });
  },
}));
