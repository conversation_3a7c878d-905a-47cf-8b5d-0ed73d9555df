import { create } from 'zustand';
import {
  LegalInfo,
  LegalResponse,
  LegalUpdate,
  convertBackendToFrontend,
  fetchLegal,
  updateLegal,
} from '@/api/legal';
import {
  PersonalizationCardField,
  PersonalizationCardFile,
} from '@/components/PersonalizationCard/PersonalizationCard.types';

const DEFAULT_PAGE_SIZE = 10;

interface LegalItem {
  id: string;
  title: string;
  summary?: string;
  fields: PersonalizationCardField[];
  files: PersonalizationCardFile[];
  showSummarySection?: boolean;
  showDetailedSection?: boolean;
  summaryTitle?: string;
  detailedTitle?: string;
  filesTitle?: string;
}

function mapFieldsToUpdateRequest(fields: PersonalizationCardField[], title?: string): LegalUpdate {
  const typeField = fields.find((f) => f.label === 'Type');

  return {
    type: typeField?.value as any,
  };
}

interface LegalState {
  legal: LegalItem[];
  editingLegal: string[];

  currentPage: number;
  totalPages: number | null;
  hasMoreItems: boolean;
  isLoadingMore: boolean;

  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isFetching: boolean;

  fetchLegal: (token: string, reset?: boolean) => Promise<void>;
  loadMoreLegal: (token: string) => Promise<void>;
  updateLegal: (
    id: string,
    updatedFields: PersonalizationCardField[],
    updatedFiles: PersonalizationCardFile[],
    updatedTitle: string | undefined,
    token: string
  ) => Promise<void>;

  setEditingLegal: (id: string) => void;
  cancelEditingLegal: (id: string) => void;
  removeFileFromLegal: (legalId: string, fileId: string) => void;
  addFileBasedLegal: (file: PersonalizationCardFile) => void;

  reset: () => void;
}

export const useLegal = create<LegalState>((set, get) => ({
  legal: [],
  editingLegal: [],

  currentPage: 1,
  totalPages: null,
  hasMoreItems: true,
  isLoadingMore: false,

  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isFetching: false,

  fetchLegal: async (token: string, reset?: boolean) => {
    set({ isFetching: true });
    try {
      if (reset) {
        set({ legal: [], currentPage: 1 });
      }
      const response: LegalResponse = await fetchLegal({
        token,
        page: reset ? 1 : get().currentPage,
        size: DEFAULT_PAGE_SIZE,
      });
      const convertedLegal = response.items.map(convertBackendToFrontend);
      set((state) => ({
        legal: reset ? convertedLegal : [...state.legal, ...convertedLegal],
        totalPages: response.pages,
        hasMoreItems:
          response.page !== null && response.pages !== null && response.page < response.pages,
      }));
    } catch (error) {
      console.error('Failed to fetch legal documents:', error);
    } finally {
      set({ isFetching: false });
    }
  },

  loadMoreLegal: async (token: string) => {
    set({ isLoadingMore: true });
    try {
      const currentPage = get().currentPage;
      const response: LegalResponse = await fetchLegal({
        token,
        page: currentPage + 1,
        size: DEFAULT_PAGE_SIZE,
      });
      const convertedLegal = response.items.map(convertBackendToFrontend);
      set((state) => ({
        legal: [...state.legal, ...convertedLegal],
        totalPages: response.pages,
        hasMoreItems:
          response.page !== null && response.pages !== null && response.page < response.pages,
        currentPage: currentPage + 1,
      }));
    } catch (error) {
      console.error('Failed to load more legal documents:', error);
    } finally {
      set({ isLoadingMore: false });
    }
  },

  updateLegal: async (
    id: string,
    updatedFields: PersonalizationCardField[],
    updatedFiles: PersonalizationCardFile[],
    updatedTitle: string | undefined,
    token: string
  ) => {
    set({ isUpdating: true });
    try {
      const legalId = parseInt(id);
      if (isNaN(legalId)) {
        throw new Error('Invalid legal document ID');
      }

      const updateRequest: LegalUpdate = mapFieldsToUpdateRequest(updatedFields, updatedTitle);

      const updatedLegal: LegalInfo = await updateLegal({
        legalId,
        request: updateRequest,
        token,
      });

      const convertedLegal = convertBackendToFrontend(updatedLegal);

      set((state) => ({
        legal: state.legal.map((legalDoc) => (legalDoc.id === id ? convertedLegal : legalDoc)),
      }));
    } catch (error) {
      console.error('Failed to update legal document:', error);
      set((state) => ({
        legal: state.legal.map((legalDoc) =>
          legalDoc.id === id
            ? {
                ...legalDoc,
                title: updatedTitle || legalDoc.title,
                fields: updatedFields,
                files: updatedFiles,
              }
            : legalDoc
        ),
      }));
    } finally {
      set({ isUpdating: false });
    }
  },

  setEditingLegal: (id: string) => {
    set((state) => ({
      editingLegal: [...state.editingLegal, id],
    }));
  },

  cancelEditingLegal: (id: string) => {
    set((state) => ({
      editingLegal: state.editingLegal.filter((legalId) => legalId !== id),
    }));
  },

  removeFileFromLegal: (legalId: string, fileId: string) => {
    set((state) => ({
      legal: state.legal.map((legalDoc) =>
        legalDoc.id === legalId
          ? {
              ...legalDoc,
              files: legalDoc.files.filter((file) => file.id.toString() !== fileId),
            }
          : legalDoc
      ),
    }));
  },

  addFileBasedLegal: (file: PersonalizationCardFile) => {
    const legalId = `legal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const newLegal: LegalItem = {
      id: legalId,
      title: file.fileName.replace(/\.[^/.]+$/, ''),
      fields: [
        {
          id: '1',
          label: 'Type',
          value: '',
          editable: true,
          placeholder: 'e.g. Tenancy Agreement',
          type: 'text' as const,
        },
        {
          id: '2',
          label: 'Summary',
          value: '',
          editable: true,
          placeholder: 'Brief description',
          type: 'text' as const,
        },
        {
          id: '3',
          label: 'Details',
          value: '',
          editable: true,
          placeholder: 'Additional details',
          type: 'text' as const,
        },
      ],
      files: [file],
    };

    set((state) => ({
      legal: [...state.legal, newLegal],
      editingLegal: [...state.editingLegal, legalId],
    }));
  },

  reset: () => {
    set({
      legal: [],
      editingLegal: [],
      currentPage: 1,
      totalPages: null,
      hasMoreItems: true,
      isLoadingMore: false,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isFetching: false,
    });
  },
}));
